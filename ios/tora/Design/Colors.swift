import SwiftUI

extension Color {
    struct CatppuccinMocha {
        static let base = Color(red: 0x1e / 255, green: 0x1e / 255, blue: 0x2e / 255)
        static let mantle = Color(red: 0x18 / 255, green: 0x18 / 255, blue: 0x25 / 255)
        static let crust = Color(red: 0x11 / 255, green: 0x11 / 255, blue: 0x1b / 255)
        static let surface0 = Color(red: 0x31 / 255, green: 0x32 / 255, blue: 0x44 / 255)
        static let surface1 = Color(red: 0x45 / 255, green: 0x47 / 255, blue: 0x5a / 255)
        static let surface2 = Color(red: 0x58 / 255, green: 0x5b / 255, blue: 0x70 / 255)
        static let overlay0 = Color(red: 0x6c / 255, green: 0x70 / 255, blue: 0x86 / 255)
        static let overlay1 = Color(red: 0x7f / 255, green: 0x84 / 255, blue: 0x9c / 255)
        static let overlay2 = Color(red: 0x93 / 255, green: 0x99 / 255, blue: 0xb2 / 255)
        static let text = Color(red: 0xe0 / 255, green: 0xe7 / 255, blue: 0xff / 255)
        static let subtext0 = Color(red: 0xa6 / 255, green: 0xad / 255, blue: 0xc8 / 255)
        static let subtext1 = Color(red: 0xba / 255, green: 0xc2 / 255, blue: 0xde / 255)
        static let lavender = Color(red: 0xb4 / 255, green: 0xbe / 255, blue: 0xfe / 255)
        static let blue = Color(red: 0x89 / 255, green: 0xb4 / 255, blue: 0xfa / 255)
        static let sapphire = Color(red: 0x74 / 255, green: 0xc7 / 255, blue: 0xec / 255)
        static let sky = Color(red: 0x89 / 255, green: 0xdc / 255, blue: 0xeb / 255)
        static let teal = Color(red: 0x94 / 255, green: 0xe2 / 255, blue: 0xd5 / 255)
        static let green = Color(red: 0xa6 / 255, green: 0xe3 / 255, blue: 0xa1 / 255)
        static let yellow = Color(red: 0xf9 / 255, green: 0xe2 / 255, blue: 0xaf / 255)
        static let peach = Color(red: 0xfa / 255, green: 0xb3 / 255, blue: 0x87 / 255)
        static let maroon = Color(red: 0xeb / 255, green: 0xa0 / 255, blue: 0xac / 255)
        static let red = Color(red: 0xf3 / 255, green: 0x8b / 255, blue: 0xa8 / 255)
        static let mauve = Color(red: 0xcb / 255, green: 0xa6 / 255, blue: 0xf7 / 255)
        static let pink = Color(red: 0xf5 / 255, green: 0xc2 / 255, blue: 0xe7 / 255)
        static let flamingo = Color(red: 0xf2 / 255, green: 0xcd / 255, blue: 0xcd / 255)
        static let rosewater = Color(red: 0xf5 / 255, green: 0xe0 / 255, blue: 0xdc / 255)
    }

    struct CatppuccinLatte {
        static let base = Color(red: 0xef / 255, green: 0xf1 / 255, blue: 0xf5 / 255)
        static let mantle = Color(red: 0xe6 / 255, green: 0xe9 / 255, blue: 0xef / 255)
        static let crust = Color(red: 0xdc / 255, green: 0xe0 / 255, blue: 0xe8 / 255)
        static let surface0 = Color(red: 0xcc / 255, green: 0xd0 / 255, blue: 0xda / 255)
        static let surface1 = Color(red: 0xbc / 255, green: 0xc0 / 255, blue: 0xcc / 255)
        static let surface2 = Color(red: 0xac / 255, green: 0xb0 / 255, blue: 0xbe / 255)
        static let overlay0 = Color(red: 0x9c / 255, green: 0xa0 / 255, blue: 0xb0 / 255)
        static let overlay1 = Color(red: 0x8c / 255, green: 0x8f / 255, blue: 0xa1 / 255)
        static let overlay2 = Color(red: 0x7c / 255, green: 0x7f / 255, blue: 0x93 / 255)
        static let text = Color(red: 0x4c / 255, green: 0x4f / 255, blue: 0x69 / 255)
        static let subtext0 = Color(red: 0x6c / 255, green: 0x6f / 255, blue: 0x85 / 255)
        static let subtext1 = Color(red: 0x5c / 255, green: 0x5f / 255, blue: 0x77 / 255)
        static let lavender = Color(red: 0x72 / 255, green: 0x87 / 255, blue: 0xfd / 255)
        static let blue = Color(red: 0x1e / 255, green: 0x66 / 255, blue: 0xf5 / 255)
        static let sapphire = Color(red: 0x20 / 255, green: 0x9f / 255, blue: 0xb5 / 255)
        static let sky = Color(red: 0x04 / 255, green: 0xa5 / 255, blue: 0xe5 / 255)
        static let teal = Color(red: 0x17 / 255, green: 0x92 / 255, blue: 0x99 / 255)
        static let green = Color(red: 0x40 / 255, green: 0xa0 / 255, blue: 0x2b / 255)
        static let yellow = Color(red: 0xdf / 255, green: 0x8e / 255, blue: 0x1d / 255)
        static let peach = Color(red: 0xfe / 255, green: 0x64 / 255, blue: 0x0b / 255)
        static let maroon = Color(red: 0xe6 / 255, green: 0x45 / 255, blue: 0x53 / 255)
        static let red = Color(red: 0xd2 / 255, green: 0x0f / 255, blue: 0x39 / 255)
        static let mauve = Color(red: 0x88 / 255, green: 0x39 / 255, blue: 0xef / 255)
        static let pink = Color(red: 0xea / 255, green: 0x76 / 255, blue: 0xcb / 255)
        static let flamingo = Color(red: 0xdd / 255, green: 0x78 / 255, blue: 0x78 / 255)
        static let rosewater = Color(red: 0xdc / 255, green: 0x8a / 255, blue: 0x78 / 255)
    }

    static var ctpBase: Color { CatppuccinLatte.base }
    static var ctpMantle: Color { CatppuccinLatte.mantle }
    static var ctpCrust: Color { CatppuccinLatte.crust }
    static var ctpSurface0: Color { CatppuccinLatte.surface0 }
    static var ctpSurface1: Color { CatppuccinLatte.surface1 }
    static var ctpSurface2: Color { CatppuccinLatte.surface2 }
    static var ctpOverlay0: Color { CatppuccinLatte.overlay0 }
    static var ctpOverlay1: Color { CatppuccinLatte.overlay1 }
    static var ctpOverlay2: Color { CatppuccinLatte.overlay2 }
    static var ctpText: Color { CatppuccinLatte.text }
    static var ctpSubtext0: Color { CatppuccinLatte.subtext0 }
    static var ctpSubtext1: Color { CatppuccinLatte.subtext1 }
    static var ctpLavender: Color { CatppuccinLatte.lavender }
    static var ctpBlue: Color { CatppuccinLatte.blue }
    static var ctpSapphire: Color { CatppuccinLatte.sapphire }
    static var ctpSky: Color { CatppuccinLatte.sky }
    static var ctpTeal: Color { CatppuccinLatte.teal }
    static var ctpGreen: Color { CatppuccinLatte.green }
    static var ctpYellow: Color { CatppuccinLatte.yellow }
    static var ctpPeach: Color { CatppuccinLatte.peach }
    static var ctpMaroon: Color { CatppuccinLatte.maroon }
    static var ctpRed: Color { CatppuccinLatte.red }
    static var ctpMauve: Color { CatppuccinLatte.mauve }
    static var ctpPink: Color { CatppuccinLatte.pink }
    static var ctpFlamingo: Color { CatppuccinLatte.flamingo }
    static var ctpRosewater: Color { CatppuccinLatte.rosewater }
}

struct ModalBackground: ViewModifier {
    func body(content: Content) -> some View {
        content
            .background(Color.ctpBase)
    }
}

extension View {
    func modalBackground() -> some View {
        modifier(ModalBackground())
    }
}
